<script setup lang="ts">
import { v4 as uuid } from 'uuid';
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';

interface Option {
  label: string;
  value: string;
}

const props = defineProps({
  id: {
    type: String,
    default() {
      return `select-${uuid()}`;
    },
  },
  options: {
    type: [Object, Array],
    required: true,
  },
  error: String,
  label: String,
  modelValue: [String, Number, Boolean],
  disabled: {
    type: Boolean,
    default: false,
  },
  hasSearch: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);
const selected = ref(props.modelValue);
const searchQuery = ref('');
const isOpen = ref(false);
const dropdownRef = ref<HTMLElement | null>(null);

const newOptions = computed(() => {
  const options: Option[] = [];
  if (props.options) {
    if (Array.isArray(props.options)) {
      return props.options;
    } else {
      Object.keys(props.options).forEach(key => {
        options.push({ label: props.options[key], value: key });
      });
    }
  }

  if (props.hasSearch && searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    return options.filter(
      option => option.label.toLowerCase().includes(query) || option.value.toLowerCase().includes(query),
    );
  }

  return options;
});

const selectedLabel = computed(() => {
  const option = newOptions.value.find(opt => opt.value == selected.value);
  return option ? option.label : '';
});

watch(selected, newSelected => {
  emit('update:modelValue', newSelected);
});

const handleSearch = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;
};

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value && props.hasSearch) {
    setTimeout(() => {
      const searchInput = dropdownRef.value?.querySelector('input');
      if (searchInput) {
        searchInput.focus();
      }
    }, 0);
  }
};

const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <div class="relative bg-transparent" ref="dropdownRef">
      <div
        @click="toggleDropdown"
        class="w-full appearance-none rounded-lg border bg-none px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden flex items-center justify-between"
        :class="[
          error ? 'error-input' : 'normal-input',
          disabled ? 'bg-gray-100 cursor-default' : 'bg-transparent cursor-pointer',
        ]"
      >
        <span>{{ selectedLabel || '' }}</span>
        <span class="text-gray-700">
          <svg
            class="stroke-current"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.79175 7.396L10.0001 12.6043L15.2084 7.396"
              stroke=""
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </div>
      <p v-if="error" class="mt-1 text-theme-sm text-error-500">{{ error }}</p>

      <div v-if="isOpen && !disabled" class="absolute z-50 w-full mt-1 bg-white rounded-lg shadow-lg border">
        <div v-if="hasSearch" class="p-2 border-b">
          <input
            type="text"
            class="w-full rounded-lg border bg-transparent px-4 py-2 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden"
            placeholder="Search..."
            @input="handleSearch"
          />
        </div>
        <div class="max-h-60 overflow-y-auto">
          <div
            v-for="option in newOptions"
            :key="option.value"
            class="px-4 py-2 text-sm text-gray-800 hover:bg-gray-100 cursor-pointer"
            :class="{ 'bg-gray-100': selected === option.value }"
            @click="
              selected = option.value;
              isOpen = false;
            "
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.max-h-60 {
  max-height: 15rem;
}
</style>
