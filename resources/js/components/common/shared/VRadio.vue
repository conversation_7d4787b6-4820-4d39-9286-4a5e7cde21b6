<script setup>
import { v4 as uuid } from 'uuid';
import { computed } from 'vue';

const props = defineProps({
  modelValue: [String, Number, Boolean],
  options: {
    type: Object,
    required: true,
  },
  name: String,
  id: {
    type: String,
    default() {
      return `radio-input-${uuid()}`;
    },
  },
  error: String,
  label: String,
});

defineEmits(['update:modelValue']);

const newOptions = computed(() => {
  const options = [];
  if (props.options) {
    Object.keys(props.options).forEach(key => {
      options.push({ label: props.options[key], value: key });
    });
  }
  return options;
});
</script>

<template>
  <div>
    <label v-if="label" class="mb-2 block text-sm font-medium text-gray-700" :for="id">{{ label }}</label>
    <div class="flex items-center">
      <div v-for="option in newOptions" :key="option.value" class="mb-2 mr-4">
        <input
          :id="`${id}-${option.value}`"
          type="radio"
          :name="name"
          :value="option.value"
          :checked="modelValue == option.value"
          class="mr-2 form-radio rounded"
          @change="$emit('update:modelValue', option.value)"
        />
        <label :for="`${id}-${option.value}`">{{ option.label }}</label>
      </div>
    </div>
    <p v-if="error" class="text-theme-sm text-error-500">{{ error }}</p>
  </div>
</template>

<style scoped></style>
