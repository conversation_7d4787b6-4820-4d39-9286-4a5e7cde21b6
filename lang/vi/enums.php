<?php

use Src\Enums\AccountRole;
use Src\Enums\ApprovalStatus;
use Src\Enums\Boolean;
use Src\Enums\CertificateLevel;
use Src\Enums\DepositType;
use Src\Enums\EmergencyRelation;
use Src\Enums\FeeType;
use Src\Enums\Gender;
use Src\Enums\JobType;
use Src\Enums\NotificationType;
use Src\Enums\PeriodType;
use Src\Enums\RecruitmentType;
use Src\Enums\UserBankType;
use Src\Enums\UserStatus;

return [
    Gender::class => [
        Gender::MALE => 'Nam',
        Gender::FEMALE => 'Nữ',
    ],
    UserBankType::class => [
        UserBankType::CASH => 'Tiền mặt',
        UserBankType::BANK => 'Chuyển khoản',
    ],
    AccountRole::class => [
        AccountRole::SUPER => 'Super Admin',
        AccountRole::NORMAL => 'Normal Admin',
        AccountRole::READ => 'Chỉ đọc',
    ],
    ApprovalStatus::class => [
        ApprovalStatus::WAITING => 'Chờ duyệt',
        ApprovalStatus::APPROVED => 'Đã duyệt',
        ApprovalStatus::DECLINED => 'Từ chối',
    ],
    Boolean::class => [
        Boolean::YES => 'Có',
        Boolean::NO => 'Không',
    ],
    CertificateLevel::class => [
        CertificateLevel::N1 => 'N1',
        CertificateLevel::N2 => 'N2',
        CertificateLevel::N3 => 'N3',
        CertificateLevel::N4 => 'N4',
        CertificateLevel::N5 => 'N5',
        CertificateLevel::SAME_N1 => 'Tương đương N1',
        CertificateLevel::SAME_N2 => 'Tương đương N2',
        CertificateLevel::SAME_N3 => 'Tương đương N3',
        CertificateLevel::SAME_N4 => 'Tương đương N4',
        CertificateLevel::SAME_N5 => 'Tương đương N5',
    ],
    EmergencyRelation::class => [
        EmergencyRelation::FAMILY => 'Gia đình',
        EmergencyRelation::RELATIVE => 'Họ hàng',
        EmergencyRelation::FRIEND => 'Bạn bè',
        EmergencyRelation::SCHOOL => 'Trường học',
    ],
    JobType::class => [
        JobType::FULL_TIME => 'Toàn thời gian',
        JobType::PART_TIME => 'Bán thời gian',
        JobType::OTHER => 'Khác'
    ],
    NotificationType::class => [
        NotificationType::PUBLIC => 'Công khai',
        NotificationType::SYSTEM => 'Hệ thống',
        NotificationType::JOB => 'Công việc'
    ],
    FeeType::class => [
        FeeType::DAY => 'Ngày',
        FeeType::HOUR => 'Giờ'
    ],
    DepositType::class => [
        DepositType::NORMAL => 'Tài khoản thông thường',
        DepositType::CURRENT => 'Tài khoản vãng lai',
        DepositType::FIXED => 'Tài khoản định kỳ',
    ],
    RecruitmentType::class => [
        RecruitmentType::ADMIN => 'Cần phê duyệt',
        RecruitmentType::AUTO => 'Phê duyệt tự động'
    ],
    UserStatus::class => [
        UserStatus::INITIAL => 'Khởi tạo',
        UserStatus::APPROVED => 'Đã duyệt',
        UserStatus::DISABLED => 'Vô hiệu hóa'
    ],
    PeriodType::class => [
        PeriodType::STUDENT => 'Du học',
        PeriodType::FAMILY => 'Lưu trú gia đình',
        PeriodType::SPECIFIC_ACTIVITY => 'Hoạt động cụ thể',
        PeriodType::PERMANENT_RESIDENT => 'Vĩnh trú',
        PeriodType::SPOUSE_OF_JAPANESE => 'Kết hôn với người Nhật',
        PeriodType::SPOUSE_OF_PERMANENT_RESIDENT => 'Kết hôn với người vĩnh trú',
        PeriodType::LONG_TERM_RESIDENT => 'Định cư lâu dài',
        PeriodType::OTHER => 'Khác',
    ]
];
