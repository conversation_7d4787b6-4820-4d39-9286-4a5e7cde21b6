// Example usage of the updated useValidation composable

import { useValidation } from '@/composables/useValidation';

// Example 1: Using the old way (backward compatibility)
const oldWay = useValidation({
  name: '',
  email: '',
  password: ''
});

// Example 2: Using the new way with custom labels
const newWayWithCustomLabels = useValidation({
  initialValues: {
    name: '',
    email: '',
    password: '',
    password_confirmation: ''
  },
  labels: {
    name: 'Full Name',
    email: 'Email Address',
    password: 'Password',
    password_confirmation: 'Confirm Password'
  }
});

// Example 3: Using the new way with translation keys
const newWayWithTranslationKeys = useValidation({
  initialValues: {
    name: '',
    email: '',
    role_div: ''
  },
  labels: {
    name: 'models/user.field.name',
    email: 'models/user.field.email',
    role_div: 'models/user.field.roleDiv'
  }
});

// Example 4: Mixed approach - some custom labels, some translation keys
const mixedApproach = useValidation({
  initialValues: {
    name: '',
    email: '',
    phone: '',
    address: ''
  },
  labels: {
    name: 'Full Name',                    // Custom label
    email: 'models/user.field.email',     // Translation key
    phone: 'Phone Number',                // Custom label
    // address will use field name directly (no custom label provided)
  }
});

// Example validation rules
const validationRules = {
  name: ['required', 'min:2'],
  email: ['required', 'email'],
  password: ['required', 'min:8'],
  password_confirmation: ['required', 'same:password']
};

// Usage example
const handleValidation = () => {
  const isValid = newWayWithCustomLabels.validateForm(validationRules);
  if (isValid) {
    console.log('Form is valid!');
  } else {
    console.log('Form has errors:', newWayWithCustomLabels.form.errors);
  }
};
